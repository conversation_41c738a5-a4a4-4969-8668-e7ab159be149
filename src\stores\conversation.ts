import {
  ConversationService,
  type ConversationInfo,
} from "@/services/conversationService";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useConversationStore = defineStore("conversation", () => {
  // 状态
  const conversationList = ref<ConversationInfo[]>([]);
  const loading = ref(false);

  // 获取会话列表
  const getConversationList = async (page?: number) => {
    try {
      loading.value = true;
      const result = await ConversationService.getConversationListFromApi(page);
      conversationList.value = result.items || [];
    } finally {
      loading.value = false;
    }
  };

  // 添加新会话
  const addConversation = (conversation: ConversationInfo) => {
    conversationList.value.unshift(conversation);
  };

  // 删除会话
  const removeConversation = (id: number) => {
    const index = conversationList.value.findIndex((conv) => conv.id === id);
    if (index !== -1) {
      conversationList.value.splice(index, 1);
    }
  };

  return {
    conversationList,
    loading,
    getConversationList,
    addConversation,
    removeConversation,
  };
});
