<script lang="ts" setup>
import AiAssistantIcon from "@/components/icons/AiAssistantIcon.vue";
import DiagnosisIcon from "@/components/icons/DiagnosisIcon.vue";
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import { AREA_CODE } from "@/constants/constant";
import LoginService from "@/services/loginService";
import { useCommonStore } from "@/stores/common";
import { ElMessage } from "element-plus";
import { computed, ref } from "vue";

// Pinia 库
const commonStore = useCommonStore();
const curAreaCode = ref(AREA_CODE[0].value);
const curPhone = ref("");
const curCaptcha = ref("");
const isAllow = ref(false);
const rememberLogin = ref(false);
const countdown = ref(0);
const isGettingCaptcha = ref(false);

// 手机号格式验证
const isPhoneValid = computed(() => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(curPhone.value);
});

// 验证码格式验证
const isCaptchaValid = computed(() => {
  const captchaRegex = /^\d{6}$/;
  return curCaptcha.value && captchaRegex.test(curCaptcha.value);
});

// 登录按钮是否可用
const isLoginDisabled = computed(() => {
  return !isPhoneValid.value || !isCaptchaValid.value || !isAllow.value || commonStore.loginLoading;
});

// 获取验证码按钮是否可用
const isCaptchaDisabled = computed(() => {
  return !isPhoneValid.value || countdown.value > 0 || isGettingCaptcha.value;
});

// 获取验证码按钮文本
const captchaButtonText = computed(() => {
  if (isGettingCaptcha.value) return "发送中...";
  if (countdown.value > 0) return `${countdown.value}秒后重新获取`;
  return "获取验证码";
});

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

// 获取验证码
const getCaptcha = async () => {
  if (!isPhoneValid.value) {
    ElMessage.error("请输入正确的手机号");
    return;
  }

  try {
    isGettingCaptcha.value = true;
    await LoginService.getCaptchaFromApi(curPhone.value, curAreaCode.value);
    startCountdown();
  } catch (error) {
    console.error("获取验证码失败:", error);
  } finally {
    isGettingCaptcha.value = false;
  }
};

// 登录
const login = async () => {
  if (!isLoginDisabled.value) {
    await commonStore.login(curPhone.value, curCaptcha.value, rememberLogin.value);
  }
};

const showLoginDialog = computed({
  get: () => commonStore.showLogin,
  set: (value: boolean) => {
    commonStore.showLogin = value;
  },
});
</script>

<template>
  <div class="login-dialog">
    <el-dialog
      v-model="showLoginDialog"
      :show-close="false"
      width="60%"
      style="padding: 0"
    >
      <div class="login-content">
        <div class="login-content-left">
          <img src="/logo.png" alt="logo" />
          <div>
            <span>智能问诊</span>
            <span>知识查询</span>
          </div>
          <div>
            <span>语音录入</span>
            <span>报告分析</span>
            <span>影像诊断</span>
          </div>
        </div>
        <div class="login-content-right">
          <div class="login-content-right-title">登录后免费试用完整功能</div>
          <div class="login-content-right-subtitle">
            <span><DiagnosisIcon />辅助鉴别诊断</span>
            <span><AiAssistantIcon />AI知识助手</span>
            <span><ImageDiagnosisIcon />影像诊断助手</span>
          </div>
          <el-input-group class="login-phone-group">
            <el-select v-model="curAreaCode" class="login-phone-select">
              <el-option
                v-for="item in AREA_CODE"
                :key="item.key"
                :label="item.value"
                :value="item.value"
              >
                <span class="login-phone-area-code">{{ item.value }}</span>
                <span class="login-phone-area-place">{{ item.label }}</span>
              </el-option>
            </el-select>
            <el-input
              v-model="curPhone"
              placeholder="请输入手机号"
              class="login-phone-input"
            />
          </el-input-group>
          <el-input-group class="login-captcha-group">
            <el-input
              class="login-captcha-input"
              v-model="curCaptcha"
              placeholder="请输入6位数字验证码"
              maxlength="6"
              type="tel"
            />
            <button
              class="login-captcha-button"
              :class="{ disabled: isCaptchaDisabled }"
              :disabled="isCaptchaDisabled"
              @click="getCaptcha"
            >
              {{ captchaButtonText }}
            </button>
          </el-input-group>
          <div class="checkbox-group">
            <el-checkbox v-model="rememberLogin" class="remember-checkbox">
              记住登录（30天内免登录）
            </el-checkbox>
            <el-checkbox v-model="isAllow" class="policy-checkbox">
              我已阅读并同意
              <a href="/terms" target="_blank" rel="noopener" class="policy-link"
                >《用户协议》</a
              >
              和
              <a
                href="/privacy"
                target="_blank"
                rel="noopener"
                class="policy-link"
                >《隐私政策》</a
              >
            </el-checkbox>
          </div>
          <button
            class="login-button"
            :class="{ disabled: isLoginDisabled }"
            :disabled="isLoginDisabled"
            @click="login"
          >
            {{ commonStore.loginLoading ? "登录中..." : "登录" }}
          </button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped src="./LoginDialog.css"></style>
