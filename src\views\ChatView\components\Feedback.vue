<script setup lang="ts">
import { LoginService, type FeedbackInfo } from "@/services/loginService";
import { ElMessage } from "element-plus";
import { computed, reactive, ref } from "vue";

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 反馈类型选项
const feedbackTypes = [
  { label: "功能建议", value: 1 },
  { label: "界面设计", value: 2 },
  { label: "性能问题", value: 3 },
  { label: "其他问题", value: 4 },
];

// 表单数据
const formData = reactive<FeedbackInfo>({
  feedback_type: 1,
  feedback_content: "",
  contact_email: "",
  contact_phone: "",
});

// 表单引用和状态
const formRef = ref();
const loading = ref(false);

// 表单验证规则
const rules = {
  feedback_content: [
    { required: true, message: "请输入反馈内容", trigger: "blur" },
    { min: 1, max: 500, message: "反馈内容长度在 1 到 500 个字符", trigger: "blur" },
  ],
  contact_email: [
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: "请输入正确的邮箱地址",
      trigger: "blur",
    },
  ],
  contact_phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 选择反馈类型
const selectFeedbackType = (type: number) => {
  formData.feedback_type = type;
};

// 重置表单
const resetForm = () => {
  formData.feedback_type = 1;
  formData.feedback_content = "";
  formData.contact_email = "";
  formData.contact_phone = "";
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 提交反馈
const submitFeedback = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    loading.value = true;

    // 调用API提交反馈
    await LoginService.createFeedbackFromApi(formData);

    ElMessage.success("反馈提交成功，感谢您的建议！");
    handleClose();
  } catch (error) {
    console.error("提交反馈失败:", error);
    if (error !== "validation failed") {
      ElMessage.error("提交失败，请稍后重试");
    }
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="意见反馈"
    width="35dvw"
    :before-close="handleClose"
    class="feedback-dialog"
    center
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      class="feedback-form"
    >
      <!-- 反馈类型 -->
      <div class="form-section">
        <h3 class="section-title">反馈类型</h3>
        <div class="feedback-types">
          <div
            v-for="type in feedbackTypes"
            :key="type.value"
            :class="[
              'feedback-type-item',
              { active: formData.feedback_type === type.value }
            ]"
            @click="selectFeedbackType(type.value)"
          >
            {{ type.label }}
          </div>
        </div>
      </div>

      <!-- 反馈内容 -->
      <div class="form-section">
        <h3 class="section-title">反馈内容</h3>
        <el-form-item prop="feedback_content">
          <el-input
            v-model="formData.feedback_content"
            type="textarea"
            placeholder="请详细描述您的建议..."
            :rows="6"
            maxlength="500"
            show-word-limit
            class="feedback-textarea"
          />
        </el-form-item>
      </div>

      <!-- 联系方式 -->
      <div class="form-section">
        <h3 class="section-title">联系方式</h3>
        <el-form-item prop="contact_email">
          <el-input
            v-model="formData.contact_email"
            placeholder="请输入邮箱地址"
            class="contact-input"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="contact_phone">
          <el-input
            v-model="formData.contact_phone"
            placeholder="请输入手机号码"
            class="contact-input"
          >
            <template #prefix>
              <el-icon><Phone /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-button">
          取消
        </el-button>
        <el-button
          type="primary"
          @click="submitFeedback"
          :loading="loading"
          class="submit-button"
        >
          提交反馈
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
/* 对话框样式 */
.feedback-dialog :deep(.el-dialog) {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 1dvw;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.feedback-dialog :deep(.el-dialog__header) {
  padding: 1.5dvw 2dvw 1dvw;
  border-bottom: 1px solid rgba(0, 87, 255, 0.1);
}

.feedback-dialog :deep(.el-dialog__title) {
  font-size: 1.2dvw;
  font-weight: 600;
  color: #0057ff;
}

.feedback-dialog :deep(.el-dialog__body) {
  padding: 1.5dvw 2dvw;
}

/* 表单样式 */
.feedback-form {
  display: flex;
  flex-direction: column;
  gap: 1.5dvw;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 0.8dvw;
}

.section-title {
  font-size: 1dvw;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 反馈类型选择 */
.feedback-types {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.8dvw;
}

.feedback-type-item {
  padding: 0.8dvw 1.2dvw;
  border: 1px solid #e0e0e0;
  border-radius: 0.4dvw;
  text-align: center;
  font-size: 0.9dvw;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.feedback-type-item:hover {
  border-color: #0057ff;
  color: #0057ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 87, 255, 0.1);
}

.feedback-type-item.active {
  background: linear-gradient(135deg, #0057ff 0%, #003db3 100%);
  border-color: #0057ff;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 87, 255, 0.3);
}

/* 输入框样式 */
.feedback-textarea :deep(.el-textarea__inner) {
  border-radius: 0.4dvw;
  border: 1px solid #e0e0e0;
  font-size: 0.9dvw;
  line-height: 1.4;
  resize: none;
  transition: all 0.3s ease;
}

.feedback-textarea :deep(.el-textarea__inner):focus {
  border-color: #0057ff;
  box-shadow: 0 0 0 2px rgba(0, 87, 255, 0.1);
}

.contact-input {
  margin-bottom: 0.8dvw;
}

.contact-input :deep(.el-input__wrapper) {
  border-radius: 0.4dvw;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
}

.contact-input :deep(.el-input__wrapper):hover {
  border-color: #0057ff;
}

.contact-input :deep(.el-input__wrapper.is-focus) {
  border-color: #0057ff;
  box-shadow: 0 0 0 2px rgba(0, 87, 255, 0.1);
}

.contact-input :deep(.el-input__inner) {
  font-size: 0.9dvw;
  padding-left: 0.5dvw;
}

.contact-input :deep(.el-input__prefix) {
  color: #999;
}

/* 表单项样式 */
.feedback-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.feedback-form :deep(.el-form-item__error) {
  font-size: 0.8dvw;
  margin-top: 0.3dvw;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1dvw;
  padding: 1dvw 0 0;
  border-top: 1px solid rgba(0, 87, 255, 0.1);
}

.cancel-button {
  padding: 0.6dvw 1.5dvw;
  font-size: 0.9dvw;
  border-radius: 0.4dvw;
  border: 1px solid #e0e0e0;
  background: #fff;
  color: #666;
  transition: all 0.3s ease;
}

.cancel-button:hover {
  border-color: #0057ff;
  color: #0057ff;
}

.submit-button {
  padding: 0.6dvw 1.5dvw;
  font-size: 0.9dvw;
  border-radius: 0.4dvw;
  background: linear-gradient(135deg, #0057ff 0%, #003db3 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.submit-button:hover:not(.is-loading)::before {
  left: 100%;
}

.submit-button:hover:not(.is-loading) {
  background: linear-gradient(135deg, #003db3 0%, #0057ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 87, 255, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-dialog :deep(.el-dialog) {
    width: 90dvw !important;
    margin: 5dvh auto;
  }

  .feedback-dialog :deep(.el-dialog__title) {
    font-size: 4dvw;
  }

  .section-title {
    font-size: 3.5dvw;
  }

  .feedback-types {
    grid-template-columns: 1fr;
    gap: 1.5dvh;
  }

  .feedback-type-item {
    padding: 2dvh 3dvw;
    font-size: 3.5dvw;
  }

  .feedback-textarea :deep(.el-textarea__inner) {
    font-size: 3.5dvw;
  }

  .contact-input :deep(.el-input__inner) {
    font-size: 3.5dvw;
  }

  .cancel-button,
  .submit-button {
    padding: 2dvh 4dvw;
    font-size: 3.5dvw;
  }

  .dialog-footer {
    gap: 3dvw;
  }
}
</style>
