<script lang="ts" setup>
import DeleteIcon from "@/components/icons/DeleteIcon.vue";
import EditIcon from "@/components/icons/EditIcon.vue";
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import LabReportIcon from "@/components/icons/LabReportIcon.vue";
import MoreDotsIcon from "@/components/icons/MoreDotsIcon.vue";
import NewChatIcon from "@/components/icons/NewChatIcon.vue";
import StopIcon from "@/components/icons/StopIcon.vue";
import SubmitArrowIcon from "@/components/icons/SubmitArrowIcon.vue";
import { FUNCTION_LIST, type FunctionItem } from "@/constants/constant";
import { ChatService, type ChatMessage } from "@/services/chatService";
import {
  ConversationService,
  type ConversationInfo,
} from "@/services/conversationService";
import { useChatStore } from "@/stores/chat";
import { useConversationStore } from "@/stores/conversation";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, onMounted, ref } from "vue";
import ChatBubble from "./components/ChatBubble.vue";

export type ImageUploadStatus = "uploading" | "success" | "error";
export interface ImageAttachment {
  name: string;
  ocrText?: string;
  size: number;
  status?: ImageUploadStatus;
}

//Pina库
const conversationStore = useConversationStore();
const chatStore = useChatStore();

onMounted(async () => {
  await conversationStore.getConversationList();
});

const cur_function = ref(FUNCTION_LIST[0]);
const cur_conversation = ref();
const senderValue = ref("");
const uploadResultImages = ref<ImageAttachment[]>([]);
const uploadReportImages = ref<ImageAttachment[]>([]);
const isAiTyping = ref(false);

// UI 相关状态
const disabled = ref(false);
const uploadedImages = computed(() => [
  ...uploadResultImages.value,
  ...uploadReportImages.value,
]);

// 重命名相关状态
const renamingConversationId = ref<number | null>(null);
const renameInputValue = ref("");

const getBubbleList = computed(() => {
  // 如果有选中的对话，显示历史消息
  return chatStore.messageList.map((item: any) => {
    let bubble: ChatMessage = {
      id: item.id,
      type: item.type,
      content: item.content,
      timestamp: new Date(item.created_at).getTime(),
      loading: false,
      thinkLoading: false,
      thinking: "",
      uploadResultImages: [],
      uploadReportImages: [],
    };
    if (item.role) {
      bubble.type = item.role === 1 ? "user" : "ai";
    }
    return bubble;
  });
});

const addMessage = (
  message: string,
  type: "user" | "ai" = "user",
  uploadResultImages?: ImageAttachment[],
  uploadReportImages?: ImageAttachment[]
) => {
  const newMessage: ChatMessage = {
    id: Date.now() + Math.random(),
    type,
    content: message,
    timestamp: Date.now(),
    loading: type === "ai",
    thinkLoading: type === "ai",
    uploadResultImages,
    uploadReportImages,
  };

  chatStore.messageList.push(newMessage);
  return newMessage;
};

// 更新消息内容的函数
const updateMessage = (
  messageId: number,
  content: string,
  thinking?: string,
  finished?: boolean,
  thinkFinished?: boolean
) => {
  const messageIndex = chatStore.messageList.findIndex(
    (msg) => msg.id === messageId
  );
  if (messageIndex !== -1) {
    const message = chatStore.messageList[messageIndex];
    message.content = content;
    if (thinking !== undefined) {
      message.thinking = thinking;
    }
    if (finished !== undefined) {
      message.loading = !finished;
    }
    if (thinkFinished !== undefined) {
      message.thinkLoading = !thinkFinished;
    }
  }
};

const sendMessage = async (
  message: string,
  uploadResultImages?: ImageAttachment[],
  uploadReportImages?: ImageAttachment[]
) => {
  if (
    !message.trim() &&
    (!uploadResultImages || uploadResultImages.length === 0) &&
    (!uploadReportImages || uploadReportImages.length === 0)
  )
    return;

  // 添加用户消息
  addMessage(message, "user", uploadResultImages, uploadReportImages);

  // 设置AI正在输入状态
  isAiTyping.value = true;

  try {
    // 创建一个空的 AI 回复消息，用于流式更新
    const aiMessage = addMessage("", "ai");

    // 准备附件数据
    const attachments = [
      ...(uploadResultImages || []),
      ...(uploadReportImages || []),
    ];

    // 使用流式响应
    await ChatService.sendMessageStream(
      {
        message: message,
        attachments: attachments.length > 0 ? attachments : undefined,
        conversationId: cur_conversation.value?.conversation_id,
      },
      (chunk) => {
        // 更新消息内容
        updateMessage(
          aiMessage.id,
          chunk.content,
          chunk.thinking,
          chunk.finished,
          chunk.thinkFinished
        );
      }
    );
  } catch (error) {
    console.error("发送消息失败:", error);
    handleErrorMessage("抱歉，我现在无法回复您的消息，请稍后再试。");
  } finally {
    isAiTyping.value = false;
  }
};

const handleSendMessage = async () => {
  const message = senderValue.value.trim();

  if (!message && uploadedImages.value.length === 0) {
    ElMessage.warning("请输入消息内容或上传图片");
    return;
  }

  if (isAiTyping.value) {
    ElMessage.warning("AI正在回复中，请稍候...");
    return;
  }

  if (!cur_conversation.value) {
    conversationStore.getConversationList();
  }

  // 发送消息和附件
  await sendMessage(
    message || "根据上传的图片回答问题",
    uploadResultImages.value.length > 0 ? uploadResultImages.value : undefined,
    uploadReportImages.value.length > 0 ? uploadReportImages.value : undefined
  );

  // 清空输入框和附件
  senderValue.value = "";
  uploadResultImages.value = [];
  uploadReportImages.value = [];
};

// 停止AI回复
const handleStopResponse = () => {
  ChatService.abortCurrentRequest();
  isAiTyping.value = false;
  handleErrorMessage("输出已中断");
};

function handleErrorMessage(msg: string = "输出已中断，请重新发送消息") {
  const lastAiMessage = chatStore.messageList
    .slice()
    .reverse()
    .find((msg) => msg.type === "ai");
  if (lastAiMessage) {
    lastAiMessage.loading = false;
    lastAiMessage.thinkLoading = false;
    if (!lastAiMessage.content) {
      lastAiMessage.content = msg;
    }
  }
}
function clickFunction(item: FunctionItem) {
  cur_function.value = item;
  cur_conversation.value = null;
}

function clickHistory(item: ConversationInfo) {
  cur_conversation.value = item;
  chatStore.getMessageList(item.conversation_id);
}

function clickNewChat() {
  cur_conversation.value = null;
  chatStore.messageList = [];
}

// 处理三点菜单命令
const handleMenuCommand = (command: string, conversation: ConversationInfo) => {
  switch (command) {
    case "rename":
      startRename(conversation);
      break;
    case "delete":
      handleDeleteConversation(conversation);
      break;
    default:
      break;
  }
};

// 开始重命名
const startRename = (conversation: ConversationInfo) => {
  renamingConversationId.value = conversation.id;
  renameInputValue.value = conversation.title;
};

// 取消重命名
const cancelRename = () => {
  renamingConversationId.value = null;
  renameInputValue.value = "";
};

// 确认重命名
const confirmRename = async (conversation: ConversationInfo) => {
  if (!renameInputValue.value.trim()) {
    ElMessage.warning("请输入新的对话标题");
    return;
  }

  await ConversationService.renameConversationFromApi(
    conversation.id,
    renameInputValue.value.trim()
  );

  // 更新本地状态
  conversationStore.renameConversation(
    conversation.id,
    renameInputValue.value.trim()
  );

  // 如果当前选中的是被重命名的对话，也要更新
  if (cur_conversation.value?.id === conversation.id) {
    cur_conversation.value.title = renameInputValue.value.trim();
  }

  cancelRename();
};

// 处理删除对话
const handleDeleteConversation = async (conversation: ConversationInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除对话"${conversation.title}"吗？删除后无法恢复。`,
      "删除确认",
      {
        confirmButtonText: "删除",
        cancelButtonText: "取消",
        type: "warning",
        confirmButtonClass: "el-button--danger",
      }
    );

    await ConversationService.deleteConversationFromApi(conversation.id);

    // 更新本地状态
    conversationStore.removeConversation(conversation.id);

    // 如果删除的是当前选中的对话，清空选中状态
    if (cur_conversation.value?.id === conversation.id) {
      cur_conversation.value = null;
      chatStore.messageList = [];
    }

    ElMessage.success("删除成功");
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除失败:", error);
      ElMessage.error("删除失败，请重试");
    }
  }
};
</script>

<template>
  <div class="chat-view">
    <div class="chat-slider">
      <div class="chat-function">
        <div>
          <h3>功能模块</h3>
        </div>
        <ul class="chat-function-list">
          <li
            v-for="item in FUNCTION_LIST"
            :key="item.id"
            @click="clickFunction(item)"
            :class="{ active: cur_function.id === item.id }"
          >
            <component :is="item.icon" />
            {{ item.title }}
          </li>
        </ul>
      </div>
      <div class="chat-history">
        <div class="chat-history-title">
          <h3>历史对话</h3>
          <el-icon @click="clickNewChat" style="font-size: 0.8dvw">
            <Plus />
          </el-icon>
        </div>
        <ul class="chat-history-list">
          <li
            v-for="item in conversationStore.conversationList"
            :key="item.id"
            :class="{ active: cur_conversation?.id === item.id }"
            class="conversation-item"
          >
            <div class="conversation-content" @click="clickHistory(item)">
              <!-- 重命名输入框 -->
              <el-input
                v-if="renamingConversationId === item.id"
                v-model="renameInputValue"
                size="small"
                @blur="cancelRename"
                @keyup.enter="confirmRename(item)"
                @keyup.esc="cancelRename"
                @click.stop
                class="rename-input"
                autofocus
              />
              <!-- 正常显示标题 -->
              <span v-else class="conversation-title">{{ item.title }}</span>
            </div>

            <!-- 三点菜单 -->
            <el-dropdown
              @command="(command: string) => handleMenuCommand(command, item)"
              trigger="click"
              placement="bottom-end"
              class="conversation-menu"
              @click.stop
            >
              <el-button
                type="text"
                size="small"
                class="menu-button"
                @click.stop
              >
                <MoreDotsIcon />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu class="conversation-dropdown-menu">
                  <el-dropdown-item command="rename" class="rename-item">
                    <EditIcon />
                    <span style="margin-left: 0.3dvw; color: #f56c6c"
                      >重命名</span
                    >
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" class="delete-item">
                    <DeleteIcon />
                    <span style="margin-left: 0.3dvw">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </li>
        </ul>
      </div>
    </div>
    <div class="chat-content">
      <div class="chat-content-header">
        <h3>{{ cur_function.title }}</h3>
        <div
          v-if="cur_conversation && cur_conversation.title"
          class="chat-content-header-subtitle"
        >
          （{{ cur_conversation.title }}）
        </div>
        <button class="new-chat-button" @click="clickNewChat">
          <NewChatIcon
            style="
              font-size: 0.8dvw;
              margin-right: 0.4dvw;
              transform: translateY(0.1dvw);
            "
          />
          新对话
        </button>
      </div>
      <div style="flex: 1">
        <div
          v-if="!(chatStore.messageList && chatStore.messageList.length > 0)"
          class="chat-welcome"
        >
          <img src="/logo.png" alt="Logo" class="welcome-logo" />
          <h2 class="welcome-title">{{ cur_function.subTitle }}</h2>
          <p class="welcome-subtitle">
            {{ cur_function.description }}
          </p>
        </div>
        <div
          class="chat-content-main"
          :class="{
            active: !(
              chatStore.messageList && chatStore.messageList.length > 0
            ),
          }"
        >
          <div
            v-if="chatStore.messageList && chatStore.messageList.length > 0"
            class="chat-bubble-List"
          >
            <ChatBubble
              v-for="message in getBubbleList"
              :key="message.id"
              :message="message"
            />
          </div>
          <div class="chat-input">
            <MentionSender
              v-model="senderValue"
              @submit="handleSendMessage"
              variant="updown"
              :auto-size="{ minRows: 2, maxRows: 6 }"
              :inputStyle="{ color: 'white' }"
            >
              <template #action-list style="display: flex">
                <div style="display: flex; gap: 1dvw">
                  <button class="chat-input-other-button">
                    <LabReportIcon />
                    <span style="margin-left: 0.2dvw">化验结果</span>
                  </button>
                  <button class="chat-input-other-button">
                    <ImageDiagnosisIcon />
                    <span style="margin-left: 0.2dvw">影像报告</span>
                  </button>
                </div>
                <div>
                  <!-- AI正在回复时显示停止按钮 -->
                  <el-button
                    @mousedown.stop
                    v-if="isAiTyping"
                    circle
                    @click="handleStopResponse()"
                  >
                    <el-icon class="stop-icon">
                      <StopIcon />
                    </el-icon>
                  </el-button>
                  <!-- AI未回复时显示发送按钮 -->
                  <el-button
                    @mousedown.stop
                    v-else
                    circle
                    :disabled="
                      disabled ||
                      (!senderValue.trim() &&
                        !uploadedImages.some(
                          (item) => item.status === 'success'
                        )) ||
                      uploadedImages.some((item) => item.status === 'uploading')
                    "
                    @click="handleSendMessage()"
                  >
                    <el-icon class="submit-icon">
                      <SubmitArrowIcon />
                    </el-icon>
                  </el-button>
                </div>
              </template>
            </MentionSender>
            <div class="chat-input-remarks">内容由 AI 生成，请仔细甄别</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped src="./index.css"></style>
