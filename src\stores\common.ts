import LoginService from "@/services/loginService";
import { TokenCookieManager } from "@/utils/cookieUtils";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useCommonStore = defineStore("common", () => {
  // 控制登录弹框的显示/隐藏状态
  const showLogin = ref(false);
  const loginLoading = ref(false);

  // 响应式的登录状态
  const isLogin = ref(TokenCookieManager.isLoggedIn());

  // 登录方法
  const login = async (
    phone: string,
    captcha: string,
    remember: boolean = false
  ) => {
    try {
      loginLoading.value = true;
      const result = await LoginService.loginFromApi(phone, captcha);
      if (result.access_token) {
        // 使用新的 Cookie 管理器存储 token
        TokenCookieManager.setToken(
          result.access_token,
          remember,
          result.refresh_token
        );
        // 更新响应式登录状态
        isLogin.value = true;
        // 登录成功后关闭登录弹框
        showLogin.value = false;
        window.location.reload();
      }
    } finally {
      loginLoading.value = false;
    }
  };

  // 退出登录方法
  const logout = () => {
    TokenCookieManager.removeToken();
    isLogin.value = false;
    window.location.reload();
  };

  // 检查用户是否已登录（基于 token 存在性）
  const isLoggedIn = (): boolean => {
    return TokenCookieManager.isLoggedIn();
  };

  return {
    showLogin,
    loginLoading,
    isLogin,
    login,
    logout,
    isLoggedIn,
  };
});
