<script setup lang="ts">
import type { ChatMessage } from "@/services/chatService";
import { computed } from "vue";

interface Props {
  message: ChatMessage;
}

const props = defineProps<Props>();

const isUser = computed(() => props.message.type === "user");
</script>

<template>
  <Bubble
    :content="message.content"
    :placement="isUser ? 'end' : 'start'"
    avatar="logo.png"
    avatar-size="48px"
  />
</template>

<style scoped></style>
