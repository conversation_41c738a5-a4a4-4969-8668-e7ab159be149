:deep(.el-dialog > header) {
  display: none;
}

.login-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, #87ceeb 0%, #6097fc 50%, #ff69e6 100%);
  height: 65dvh;
  position: relative;
}

.login-content-left {
  font-size: 1dvw;
  flex: 1;
}

.login-content-left img {
  width: 8dvw;
}

.login-content-right {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: rgba(255, 255, 255, 0.262);
  backdrop-filter: blur(10px);
  height: 100%;
  align-items: center;
  justify-content: center;
  gap: 1dvw;
  color: white;
}

.login-content-right-title {
  font-size: 1.6dvw;
  font-weight: bold;
  letter-spacing: 0.1dvw;
}

.login-content-right-subtitle {
  font-size: 0.8dvw;
  gap: 1dvw;
  display: flex;
}

.login-content-right-subtitle > span {
  border: 0.1dvw solid white;
  padding: 0.3dvw 1.2dvw;
  border-radius: 1.5dvw;
  background: rgba(113, 113, 113, 0.458);
  display: flex;
  align-items: center;
  gap: 0.3dvw;
}

.login-phone-group {
  width: 60%;
  display: flex;
  align-items: center;
}

.login-phone-select {
  width: 25%;
}

.login-phone-input {
  width: 75%;
}

.login-phone-area-code {
  float: left;
  margin-right: 1.5dvw;
}

.login-phone-area-place {
  float: right;
  color: var(--el-text-color-secondary);
  font-size: 0.7dvw;
}
.login-captcha-group {
  display: flex;
  width: 60%;
}

.login-captcha-input {
  width: 75%;
}

.login-captcha-button {
  width: 25%;
  background: linear-gradient(135deg, #0057ff 0%, #003db3 100%);
  color: white;
  border: none;
  border-radius: 0 4px 4px 0;
  font-size: 0.8dvw;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-captcha-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #003db3 0%, #0057ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 87, 255, 0.3);
}

.login-captcha-button.disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.login-captcha-button:active:not(.disabled) {
  transform: translateY(0);
}

/* 登录按钮样式 */
.login-button {
  width: 60%;
  height: 3dvh;
  background: linear-gradient(135deg, #0057ff 0%, #003db3 100%);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 1dvw;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.05dvw;
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.login-button:hover:not(.disabled)::before {
  left: 100%;
}

.login-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #003db3 0%, #0057ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 87, 255, 0.4);
}

.login-button.disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.6;
}

.login-button:active:not(.disabled) {
  transform: translateY(0);
}

/* 复选框组样式 */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5dvw;
  margin: 0.5dvw 0;
}

/* 记住登录复选框样式 */
.remember-checkbox {
  color: white;
  font-size: 0.8dvw;
}

.remember-checkbox :deep(.el-checkbox__label) {
  color: white;
  font-size: 0.8dvw;
}

/* 用户协议复选框样式 */
.policy-checkbox {
  color: white;
  font-size: 0.8dvw;
}

.policy-checkbox :deep(.el-checkbox__label) {
  color: white;
  font-size: 0.8dvw;
}

.policy-link {
  color: #87ceeb;
  text-decoration: none;
  transition: color 0.3s ease;
}

.policy-link:hover {
  color: #add8e6;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    height: auto;
    min-height: 80dvh;
    padding: 2dvh;
  }

  .login-content-left {
    margin-bottom: 2dvh;
  }

  .login-content-left img {
    width: 15dvw;
  }

  .login-content-right {
    width: 100%;
    padding: 3dvh 2dvw;
  }

  .login-content-right-title {
    font-size: 4dvw;
  }

  .login-content-right-subtitle {
    font-size: 2.5dvw;
    flex-direction: column;
    gap: 1dvh;
  }

  .login-phone-group,
  .login-captcha-group {
    width: 90%;
  }

  .login-captcha-button {
    font-size: 2.5dvw;
  }

  .login-button {
    width: 90%;
    height: 6dvh;
    font-size: 3dvw;
  }

  .checkbox-group {
    gap: 1dvh;
  }

  .remember-checkbox {
    font-size: 2.5dvw;
  }

  .remember-checkbox :deep(.el-checkbox__label) {
    font-size: 2.5dvw;
  }

  .policy-checkbox {
    font-size: 2.5dvw;
  }

  .policy-checkbox :deep(.el-checkbox__label) {
    font-size: 2.5dvw;
  }
}
