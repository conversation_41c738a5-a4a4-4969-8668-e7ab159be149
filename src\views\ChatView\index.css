/* 整体布局 */
.chat-view {
  display: grid;
  grid-template-columns: 1fr 5fr;
  height: 100vh;
}

.chat-view h3 {
  margin-block: 0 0.6dvw;
  font-size: 0.9dvw;
}

/* 左侧布局 */
.chat-slider {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  color: white;
}

.chat-function {
  flex: 0 0 auto;
  padding: 1.2dvw 1dvw;
}

.chat-history {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0 0.1dvw 1.2dvw 1dvw;
}

.chat-function-list {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.chat-function-list li {
  padding: 0.5dvw;
  font-size: 0.9dvw;
  line-height: 1dvw;
  margin-bottom: 0.5dvw;
  display: flex;
  align-items: center;
  gap: 0.6dvw;
  border-radius: 0.4dvw;
  transition:
    background-color 0.2s ease,
    box-shadow 0.2s ease;
}

.chat-function-list li:last-child {
  margin-bottom: 0;
}

.chat-function-list li:hover {
  background-color: rgba(255, 255, 255, 0.12);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}

.chat-history-list {
  flex: 1 1 0;
  min-height: 0;
  list-style: none;
  padding-left: 0;
  margin: 0;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(236, 179, 255, 0.4) transparent;
  padding-right: 0.5dvw;
}

.chat-history-list li {
  padding: 0.3dvw;
  font-size: 0.8dvw;
  line-height: 0.9dvw;
  margin-bottom: 0.4dvw;
  border: 0.15dvw solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  box-sizing: border-box;
}

/* 对话项布局 */
.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.3dvw;
}

.conversation-content {
  flex: 1;
  min-width: 0;
  cursor: pointer;
}

.conversation-title {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 重命名输入框 */
.rename-input {
  width: 100%;
}

.rename-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 0.2dvw;
  padding: 0.1dvw 0.3dvw;
}

.rename-input :deep(.el-input__inner) {
  color: white;
  font-size: 0.8dvw;
  line-height: 0.9dvw;
}

/* 三点菜单按钮 */
.conversation-menu {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .conversation-menu {
  opacity: 1;
}

.menu-button {
  padding: 0.2dvw;
  color: rgba(255, 255, 255, 0.6);
  border: none;
  background: transparent;
  font-size: 0.7dvw;
  min-height: auto;
  height: auto;
}

.menu-button:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
}

/* 下拉菜单样式 */
.conversation-dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.4dvw;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  min-width: 5dvw;
}

.conversation-dropdown-menu .el-dropdown-menu__item {
  display: flex;
  align-items: center;
  padding: 0.4dvw 0.6dvw;
  font-size: 0.7dvw;
  color: #333;
}

.conversation-dropdown-menu .el-dropdown-menu__item:hover {
  background: rgba(0, 87, 255, 0.1);
}

.rename-item {
  color: #f56c6c !important;
}

.delete-item {
  color: #666 !important;
}

.chat-history-list li:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border: 0.15dvw solid rgba(255, 255, 255, 0.18);
  border-radius: 0.4dvw;
  background-color: rgba(255, 255, 255, 0.12);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  cursor: pointer;
}

.chat-history-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-history-title > i {
  margin-bottom: 0.6dvw;
  margin-right: 1dvw;
  padding: 0.3dvw;
  border-radius: 0.4dvw;
  border: 0.15dvw solid transparent;
  transition:
    background-color 0.2s ease,
    box-shadow 0.2s ease,
    border-color 0.2s ease;
}

.chat-history-title > i:hover {
  background-color: rgba(255, 255, 255, 0.12);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.18);
  cursor: pointer;
}

/* 欢迎界面 */
.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  margin-block: 14dvh 2dvh;
}

.welcome-logo {
  width: 8dvw;
  height: 8dvw;
  margin-bottom: 2dvw;
  opacity: 0.9;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
}

.welcome-title {
  font-size: 1.8dvw;
  font-weight: 600;
  margin: 0 0 1dvw 0;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.welcome-subtitle {
  font-size: 1dvw;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 右侧布局 */
.chat-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: white;
}

.chat-content h3 {
  margin-bottom: 0;
  margin-right: 0.2dvw;
}

.chat-content-header {
  padding: 1.2dvw 1dvw;
  flex: 0;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.chat-content-header-subtitle {
  font-size: 0.8dvw;
}

.new-chat-button {
  font-size: 0.8dvw;
  position: absolute;
  right: 2dvw;
  top: 50%;
  transform: translateY(-50%);
  padding: 0.35dvw 1dvw;
  color: #ffffff;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.16),
    rgba(255, 255, 255, 0.08)
  );
  border: 0.15dvw solid rgba(255, 255, 255, 0.45);
  border-radius: 0.5dvw;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.35);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.25),
    0 0 12px rgba(255, 255, 255, 0.18),
    inset 0 1px 3px rgba(255, 255, 255, 0.18);
  cursor: pointer;
  white-space: nowrap;
  line-height: 1;
  user-select: none;
  font-weight: bold;
  letter-spacing: 0.05dvw;
  transition:
    background-color 0.2s ease,
    box-shadow 0.2s ease,
    border-color 0.2s ease,
    color 0.2s ease,
    transform 0.12s ease;
}

.new-chat-button:hover {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.24),
    rgba(255, 255, 255, 0.14)
  );
  border-color: rgba(255, 255, 255, 0.65);
  box-shadow:
    0 4px 14px rgba(0, 0, 0, 0.28),
    0 0 16px rgba(255, 255, 255, 0.28),
    inset 0 1px 3px rgba(255, 255, 255, 0.25);
  transform: translateY(-50%);
}

.new-chat-button:active {
  transform: translateY(-50%) scale(0.97);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.25),
    0 0 10px rgba(255, 255, 255, 0.22),
    inset 0 1px 3px rgba(255, 255, 255, 0.15);
}

.new-chat-button:focus-visible {
  outline: none;
  border-color: rgba(255, 255, 255, 0.85);
  box-shadow:
    0 0 0 0.2dvw rgba(255, 255, 255, 0.35),
    0 4px 14px rgba(255, 255, 255, 0.3);
}

.chat-content-main {
  display: flex;
  flex-direction: column;
  padding-inline: 20dvw;
  position: relative;
  max-height: 83dvh;
  height: 100%;
  justify-content: space-between;
}

.chat-content-main.active {
  max-height: initial;
  height: initial;
}

.chat-bubble-List {
  overflow: auto;
}

.chat-input {
  position: sticky;
  bottom: 0;
}

:deep(.chat-input .el-sender) {
  border-radius: 0.8dvw;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

:deep(.chat-input .el-sender-action-list) {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

:deep(.chat-input .el-sender-content) {
  border-top: none;
  padding: 0.8dvw 1dvw;
}

:deep(.chat-input .el-sender-footer) {
  border-top: none;
  padding: 0 1dvw 0.8dvw;
  display: flex;
  gap: 0.6dvw;
}

.chat-input-other-button {
  font-size: 0.8dvw;
  padding: 0.2dvw 0.8dvw;
  color: rgba(255, 255, 255, 0.85);
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 1dvw;
  cursor: pointer;
  white-space: nowrap;
  user-select: none;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  letter-spacing: 0.01dvw;
  gap: 0.15dvw;
}

.chat-input-other-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.chat-input-other-button:active {
  transform: scale(0.95);
}

.chat-input-other-button:focus-visible {
  outline: none;
  border-color: rgba(255, 255, 255, 0.85);
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
}

.chat-bubble-container {
  margin-bottom: 0.8dvw;
  display: flex;
  flex-direction: column;
  gap: 0.5dvw;
  overflow-y: auto;
  padding: 0.5dvw;
}

.chat-input-remarks {
  font-size: 0.8dvw;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1dvw;
}

.feedback-button {
  font-size: 0.7dvw;
  color: rgba(255, 255, 255, 0.8);
  padding: 0.2dvw 0.5dvw;
  border-radius: 0.2dvw;
  transition: all 0.3s ease;
  text-decoration: underline;
  border: none;
  background: transparent;
}

.feedback-button:hover {
  color: #0057ff;
  background: rgba(0, 87, 255, 0.1);
  text-decoration: none;
}
