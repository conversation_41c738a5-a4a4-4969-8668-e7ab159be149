import api from "@/utils/api";

/**
 * 会话信息接口
 */
export interface ConversationInfo {
  id: number;
  title: string;
  conversation_id: string;
  conversation_type: number;
  created_at: string;
}

export interface FeedbackInfo {
  /**
   * 反馈类型
   */
  feedback_type: number;

  /**
   * 反馈内容，1-500字符
   */
  feedback_content: string;

  /**
   * 联系邮箱
   */
  contact_email?: string;

  /**
   * 联系电话
   */
  contact_phone?: string;
}

/**
 * 登录类
 */
export class LoginService {
  /**
   * 获取验证码
   *
   */
  static async getCaptchaFromApi(
    phone: string,
    country_code: string
  ): Promise<any> {
    country_code = country_code.replace("+", "");
    await api.post(
      "api/auth/sms/send",
      {
        phone: phone,
        country_code: country_code,
      },
      {
        showError: true,
        showSuccess: true,
      }
    );
  }

  /**
   * 登录
   *
   */
  static async loginFromApi(phone: string, captcha: string): Promise<any> {
    const response = await api.post(
      "api/auth/sms-login",
      {
        phone: phone,
        code: captcha,
      },
      {
        showError: true,
        showSuccess: true,
      }
    );
    return response;
  }

  /**
   * 退出登陆
   *
   */
  static async logoutFromApi(): Promise<any> {
    const response = await api.post(
      "api/auth/logout",
      {},
      {
        showError: true,
        showSuccess: true,
      }
    );
    return response;
  }

  /**
   * 意见反馈
   *
   */
  static async createFeedbackFromApi(feedbackInfo: FeedbackInfo): Promise<any> {
    const response = await api.post(
      "api/feedback/",
      feedbackInfo,
      {
        showError: true,
        showSuccess: true,
      }
    );
    return response;
  }
}

export default LoginService;
