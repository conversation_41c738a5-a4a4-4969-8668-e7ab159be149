<template>
  <div style="display: flex; flex-direction: column; height: 100dvh">
    <AppHeader style="flex: 0" />
    <router-view style="flex: 1" />
    <LoginDialog />
  </div>
</template>

<script lang="ts" setup>
import AppHeader from "@/components/AppHeader.vue";
import { useCommonStore } from "@/stores/common";
import { TokenCookieManager } from "@/utils/cookieUtils";
import { onMounted } from "vue";
import LoginDialog from "./components/LoginDialog.vue";

const commonStore = useCommonStore();

// 初始化应用状态
const initializeApp = () => {
  const token = TokenCookieManager.getToken();
  if (token) {
    // 如果存在 token，设置到 API 请求头中
    import("@/utils/api").then(({ default: api }) => {
      api.setHeader("Authorization", `Bearer ${token}`);
    });
  }

  // 如果没有 token，显示登录弹框
  if (!commonStore.isLogin) {
    commonStore.showLogin = true;
  }
};

onMounted(() => {
  initializeApp();
});
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body {
  margin: 0;
  background: rgb(48, 51, 59);
}
</style>
