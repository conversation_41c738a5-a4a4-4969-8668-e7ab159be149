<template>
  <div class="feedback-demo">
    <div class="demo-container">
      <h1>反馈组件演示</h1>
      <p>点击下面的按钮测试反馈对话框功能</p>
      
      <el-button 
        type="primary" 
        @click="showFeedback = true"
        class="demo-button"
      >
        打开反馈对话框
      </el-button>
      
      <div class="demo-info">
        <h3>功能特性：</h3>
        <ul>
          <li>✅ 四种反馈类型选择（功能建议、界面设计、性能问题、其他问题）</li>
          <li>✅ 反馈内容输入（1-500字符限制）</li>
          <li>✅ 可选联系方式（邮箱和手机号）</li>
          <li>✅ 表单验证和错误提示</li>
          <li>✅ 加载状态和成功/失败反馈</li>
          <li>✅ 响应式设计，支持移动端</li>
          <li>✅ 符合项目设计系统的样式</li>
        </ul>
      </div>
    </div>
    
    <!-- 反馈对话框 -->
    <Feedback v-model="showFeedback" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Feedback from "./ChatView/components/Feedback.vue";

const showFeedback = ref(false);
</script>

<style scoped>
.feedback-demo {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2dvw;
}

.demo-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 1dvw;
  padding: 3dvw;
  max-width: 50dvw;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.demo-container h1 {
  color: #0057ff;
  font-size: 2dvw;
  margin-bottom: 1dvw;
}

.demo-container p {
  color: #666;
  font-size: 1dvw;
  margin-bottom: 2dvw;
}

.demo-button {
  font-size: 1dvw;
  padding: 0.8dvw 2dvw;
  border-radius: 0.5dvw;
  background: linear-gradient(135deg, #0057ff 0%, #003db3 100%);
  border: none;
  margin-bottom: 2dvw;
}

.demo-info {
  text-align: left;
  background: rgba(0, 87, 255, 0.05);
  padding: 1.5dvw;
  border-radius: 0.5dvw;
  border-left: 4px solid #0057ff;
}

.demo-info h3 {
  color: #0057ff;
  font-size: 1.1dvw;
  margin-bottom: 1dvw;
}

.demo-info ul {
  list-style: none;
  padding: 0;
}

.demo-info li {
  color: #333;
  font-size: 0.9dvw;
  margin-bottom: 0.5dvw;
  padding-left: 1dvw;
}

@media (max-width: 768px) {
  .demo-container {
    max-width: 90dvw;
    padding: 5dvw;
  }
  
  .demo-container h1 {
    font-size: 6dvw;
  }
  
  .demo-container p {
    font-size: 3.5dvw;
  }
  
  .demo-button {
    font-size: 4dvw;
    padding: 3dvh 6dvw;
  }
  
  .demo-info h3 {
    font-size: 4dvw;
  }
  
  .demo-info li {
    font-size: 3dvw;
  }
}
</style>
