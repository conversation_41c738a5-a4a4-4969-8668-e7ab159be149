import type { ChatMessage } from "@/services/chatService";
import {
  ConversationService
} from "@/services/conversationService";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useChatStore = defineStore("chat", () => {
  const messageList = ref<ChatMessage[]>([]);
  const messageLoading = ref(false);

  // 获取历史消息
  const getMessageList = async (conversationId: string, page?: number) => {
    try {
      messageLoading.value = true;
      const result = await ConversationService.getChatHistoryFromApi(
        conversationId,
        page
      );
      messageList.value = result.items || [];
    } finally {
      messageLoading.value = false;
    }
  };

  return {
    messageList,
    messageLoading,
    getMessageList,
  };
});
